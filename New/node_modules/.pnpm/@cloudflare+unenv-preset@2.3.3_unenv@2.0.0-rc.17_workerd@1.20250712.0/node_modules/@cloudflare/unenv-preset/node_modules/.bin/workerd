#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules/workerd/bin/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules/workerd/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules/workerd/bin/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules/workerd/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/workerd@1.20250712.0/node_modules:/Volumes/Works/Client Projects/AutoPatient/DermaCare/Try/New/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../workerd@1.20250712.0/node_modules/workerd/bin/workerd" "$@"
else
  exec node  "$basedir/../../../../../../workerd@1.20250712.0/node_modules/workerd/bin/workerd" "$@"
fi
