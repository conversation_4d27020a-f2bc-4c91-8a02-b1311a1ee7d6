import {
  _ignoreErrors,
  _stderr,
  _stderr<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  _stdout,
  _stdoutError<PERSON>and<PERSON>,
  _times,
  Console
} from "unenv/node/console";
export {
  Console,
  _ignoreErrors,
  _stderr,
  _stderrError<PERSON><PERSON><PERSON>,
  _stdout,
  _stdoutErrorHandler,
  _times
} from "unenv/node/console";
const workerdConsole = globalThis["console"];
export const {
  assert,
  clear,
  // @ts-expect-error undocumented public API
  context,
  count,
  countReset,
  // @ts-expect-error undocumented public API
  createTask,
  debug,
  dir,
  dirxml,
  error,
  group,
  groupCollapsed,
  groupEnd,
  info,
  log,
  profile,
  profileEnd,
  table,
  time,
  timeEnd,
  timeLog,
  timeStamp,
  trace,
  warn
} = workerdConsole;
Object.assign(workerdConsole, {
  Console,
  _ignoreErrors,
  _stderr,
  _stderrError<PERSON>and<PERSON>,
  _stdout,
  _stdoutErrorHandler,
  _times
});
export default workerdConsole;
