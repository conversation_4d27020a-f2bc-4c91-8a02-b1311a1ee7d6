import { getDb } from "@database";
import { patient } from "@database/schema";
import type { GetAPContactType, WebhookContext } from "@type";
import {
	checkAndAddToBuffer,
	generatePatientBuffer<PERSON>ey,
} from "@utils/bufferManager";
import { logError, logSyncError } from "@utils/errorLogger";
import { eq } from "drizzle-orm";
import { ccClient } from "../api";
import { transformAPContactToCCPatient } from "../helpers/dataTransform";

/**
 * Processes AP contact creation events and syncs to CC as patients
 * This handles AP → CC data flow
 *
 * @param payload - AP contact data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPContactCreate(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact create for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactCreate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact creation recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Check if contact already exists in our database
		const existingPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, payload.id))
			.limit(1);

		if (existingPatient.length > 0) {
			console.log(
				`Contact already exists as patient, CC ID: ${existingPatient[0].ccId}`,
			);
			return {
				success: true,
				message: `Contact already exists as patient. CC ID: ${existingPatient[0].ccId}`,
			};
		}

		// Validate required data
		if (!payload.email && !payload.phone) {
			const message = `Email and phone are empty, cannot create patient. AP Contact ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Transform AP contact to CC patient format
		const ccPatientData = transformAPContactToCCPatient(payload);

		// Create patient in CC
		const ccPatient = await ccClient.patient.create(ccPatientData);

		if (!ccPatient) {
			const message = `Failed to create patient in CC for AP contact ID: ${payload.id}`;
			await logSyncError(
				"AP_TO_CC_PATIENT_CREATE_FAILED",
				message,
				{ apContactId: payload.id },
				"APContactProcessor",
			);
			return {
				success: false,
				message,
			};
		}

		// Store patient in database
		await db.insert(patient).values({
			apId: payload.id,
			ccId: ccPatient.id,
			email: payload.email || "",
			phone: payload.phone || "",
			apData: payload,
			ccData: ccPatient,
			apUpdatedAt: new Date(payload.updatedAt || payload.createdAt),
			ccUpdatedAt: new Date(ccPatient.updatedAt || ccPatient.createdAt),
		});

		console.log(`Successfully created patient in CC. CC ID: ${ccPatient.id}`);

		return {
			success: true,
			message: `Patient created successfully in CC. CC ID: ${ccPatient.id}`,
		};
	} catch (error) {
		const message = `Error processing AP contact create: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_CREATE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP contact update events and syncs to CC patients
 * This handles AP → CC data flow
 *
 * @param payload - AP contact data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPContactUpdate(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact update for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactUpdate",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact update recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Find patient in database
		const dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbPatient) {
			// If patient doesn't exist, create it
			console.log(
				`Patient not found, creating new patient for AP contact ID: ${payload.id}`,
			);
			return await processAPContactCreate(payload, context);
		}

		// Validate required data
		if (!payload.email && !payload.phone) {
			const message = `Email and phone are empty, dropping update request. AP Contact ID: ${payload.id}`;
			console.log(message);
			return {
				success: false,
				message,
			};
		}

		// Transform AP contact to CC patient format
		const ccPatientData = transformAPContactToCCPatient(payload);

		let ccPatient = null;

		if (dbPatient.ccId) {
			// Update existing patient in CC
			ccPatient = await ccClient.patient.update(dbPatient.ccId, ccPatientData);

			if (!ccPatient) {
				const message = `Failed to update patient in CC for AP contact ID: ${payload.id}`;
				await logSyncError(
					"AP_TO_CC_PATIENT_UPDATE_FAILED",
					message,
					{ apContactId: payload.id, ccPatientId: dbPatient.ccId },
					"APContactProcessor",
				);
				return {
					success: false,
					message,
				};
			}

			console.log(`Successfully updated patient in CC. CC ID: ${ccPatient.id}`);
		} else {
			// Create patient in CC if not exists
			ccPatient = await ccClient.patient.create(ccPatientData);

			if (!ccPatient) {
				const message = `Failed to create patient in CC for AP contact ID: ${payload.id}`;
				await logSyncError(
					"AP_TO_CC_PATIENT_CREATE_FAILED",
					message,
					{ apContactId: payload.id },
					"APContactProcessor",
				);
				return {
					success: false,
					message,
				};
			}

			console.log(`Successfully created patient in CC. CC ID: ${ccPatient.id}`);
		}

		// Update patient in database
		await db
			.update(patient)
			.set({
				ccId: ccPatient.id,
				email: payload.email || "",
				phone: payload.phone || "",
				apData: payload,
				ccData: ccPatient,
				apUpdatedAt: new Date(payload.updatedAt || payload.createdAt),
				ccUpdatedAt: new Date(ccPatient.updatedAt || ccPatient.createdAt),
			})
			.where(eq(patient.id, dbPatient.id));

		return {
			success: true,
			message: `Patient updated successfully in CC. CC ID: ${ccPatient.id}`,
		};
	} catch (error) {
		const message = `Error processing AP contact update: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_UPDATE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}

/**
 * Processes AP contact deletion events and syncs to CC patients
 * This handles AP → CC data flow
 *
 * @param payload - AP contact data from webhook
 * @param context - Webhook processing context
 * @returns Processing result
 */
export async function processAPContactDelete(
	payload: GetAPContactType,
	context: WebhookContext,
): Promise<{ success?: boolean; message: string; skipped?: boolean }> {
	const db = getDb();

	try {
		console.log(`Processing AP contact delete for AP ID: ${payload.id}`);

		// Check buffer to prevent duplicate processing
		const bufferKey = generatePatientBufferKey(
			"ProcessAPContactDelete",
			payload.id,
		);
		if (checkAndAddToBuffer(bufferKey)) {
			return {
				success: true,
				message: `AP contact deletion recently processed, skipping. AP ID: ${payload.id}`,
			};
		}

		// Find patient in database
		const dbPatient = await db
			.select()
			.from(patient)
			.where(eq(patient.apId, payload.id))
			.limit(1)
			.then((results) => results[0]);

		if (!dbPatient) {
			console.log(
				`Patient not found for deletion, AP contact ID: ${payload.id}`,
			);
			return {
				success: true,
				message: `Patient not found, nothing to delete. AP Contact ID: ${payload.id}`,
			};
		}

		// Note: We typically don't delete patients from CC when AP contacts are deleted
		// Instead, we just remove the AP association
		console.log(
			`Removing AP association for patient. CC ID: ${dbPatient.ccId}`,
		);

		// Update patient in database to remove AP association
		await db
			.update(patient)
			.set({
				apId: null,
				apData: null,
				apUpdatedAt: null,
			})
			.where(eq(patient.id, dbPatient.id));

		return {
			success: true,
			message: `AP association removed from patient. CC ID: ${dbPatient.ccId}`,
		};
	} catch (error) {
		const message = `Error processing AP contact delete: ${error instanceof Error ? error.message : "Unknown error"}`;
		await logError(
			"AP_CONTACT_DELETE_ERROR",
			error,
			{
				apContactId: payload.id,
				requestId: context.requestId,
			},
			"APContactProcessor",
		);

		return {
			success: false,
			message,
		};
	}
}
