import { getConfig } from "@utils/configs";
import { logError } from "@utils/errorLogger";

/**
 * Request options interface for API calls
 */
export interface RequestOptions {
	/** Request URL (relative to base URL) */
	url: string;
	/** HTTP method */
	method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
	/** Request headers */
	headers?: Record<string, string>;
	/** Request body data */
	data?: Record<string, unknown> | string | FormData | null;
	/** URL parameters */
	params?: Record<string, string>;
	/** Request timeout in milliseconds */
	timeout?: number;
}

/**
 * API response interface
 */
export interface ApiResponse<T = unknown> {
	/** Response data */
	data: T;
	/** HTTP status code */
	status: number;
	/** Response headers */
	headers: Headers;
	/** Success indicator */
	ok: boolean;
}

/**
 * Error response interface for API errors
 */
interface ErrorResponse {
	message?: string;
	error?: string;
	[key: string]: unknown;
}

/**
 * API error class for structured error handling
 */
export class ApiError extends Error {
	constructor(
		message: string,
		public status: number,
		public response?: ErrorResponse,
		public request?: RequestOptions,
	) {
		super(message);
		this.name = "ApiError";
	}
}

/**
 * Creates a fetch request with timeout support
 * Cloudflare Workers compatible implementation
 *
 * @param url - Request URL
 * @param options - Fetch options
 * @param timeoutMs - Timeout in milliseconds
 * @returns Promise resolving to Response
 */
async function fetchWithTimeout(
	url: string,
	options: RequestInit,
	timeoutMs: number,
): Promise<Response> {
	const controller = new AbortController();
	const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

	try {
		const response = await fetch(url, {
			...options,
			signal: controller.signal,
		});
		clearTimeout(timeoutId);
		return response;
	} catch (error) {
		clearTimeout(timeoutId);
		if (error instanceof Error && error.name === "AbortError") {
			throw new ApiError(`Request timeout after ${timeoutMs}ms`, 408);
		}
		throw error;
	}
}

/**
 * Makes an HTTP request with retry logic and error handling
 *
 * @param baseUrl - Base URL for the API
 * @param options - Request options
 * @param defaultHeaders - Default headers to include
 * @returns Promise resolving to API response
 */
export async function makeRequest<T = unknown>(
	baseUrl: string,
	options: RequestOptions,
	defaultHeaders: Record<string, string> = {},
): Promise<ApiResponse<T>> {
	const maxRetries = Number(getConfig("maxRetries"));
	const timeout = Number(options.timeout || getConfig("requestTimeout"));

	// Build full URL
	const url = new URL(options.url, baseUrl);
	if (options.params) {
		Object.entries(options.params).forEach(([key, value]) => {
			url.searchParams.append(key, value);
		});
	}

	// Prepare headers
	const headers = {
		"Content-Type": "application/json",
		Accept: "application/json",
		...defaultHeaders,
		...options.headers,
	};

	// Prepare request body
	let body: string | undefined;
	if (options.data && ["POST", "PUT", "PATCH"].includes(options.method)) {
		body = JSON.stringify(removeNullEmptyProperties(options.data));
	}

	// Prepare fetch options
	const fetchOptions: RequestInit = {
		method: options.method,
		headers,
		body,
	};

	let lastError: Error | null = null;

	// Retry loop
	for (let attempt = 0; attempt <= maxRetries; attempt++) {
		try {
			const response = await fetchWithTimeout(
				url.toString(),
				fetchOptions,
				timeout,
			);

			// Parse response
			let responseData: T;
			const contentType = response.headers.get("content-type");

			if (contentType?.includes("application/json")) {
				responseData = await response.json();
			} else {
				responseData = (await response.text()) as T;
			}

			// Handle HTTP errors
			if (!response.ok) {
				const errorMessage =
					typeof responseData === "object" &&
					responseData &&
					"message" in responseData
						? (responseData as ErrorResponse).message || `HTTP ${response.status}: ${response.statusText}`
						: `HTTP ${response.status}: ${response.statusText}`;

				throw new ApiError(
					errorMessage,
					response.status,
					responseData as ErrorResponse,
					options,
				);
			}

			return {
				data: responseData,
				status: response.status,
				headers: response.headers,
				ok: response.ok,
			};
		} catch (error) {
			lastError = error instanceof Error ? error : new Error(String(error));

			// Don't retry on client errors (4xx) except 429 (rate limit)
			if (
				error instanceof ApiError &&
				error.status >= 400 &&
				error.status < 500 &&
				error.status !== 429
			) {
				break;
			}

			// Don't retry on the last attempt
			if (attempt === maxRetries) {
				break;
			}

			// Wait before retrying (exponential backoff)
			const delay = Math.min(1000 * 2 ** attempt, 10000);
			await new Promise((resolve) => setTimeout(resolve, delay));
		}
	}

	// Log the error
	if (lastError) {
		await logError(
			"API_REQUEST_ERROR",
			lastError,
			{
				url: url.toString(),
				method: options.method,
				attempts: maxRetries + 1,
			},
			"ApiRequest",
		);

		throw lastError;
	}

	// This should never happen, but just in case
	throw new ApiError("Unknown error occurred", 500);
}

/**
 * Removes null and empty properties from an object
 * Useful for cleaning request payloads
 *
 * @param obj - Object to clean
 * @returns Cleaned object
 */
function removeNullEmptyProperties(obj: unknown): unknown {
	if (obj === null || obj === undefined) {
		return obj;
	}

	if (Array.isArray(obj)) {
		return obj.map(removeNullEmptyProperties);
	}

	if (typeof obj === "object") {
		const cleaned: Record<string, unknown> = {};
		for (const [key, value] of Object.entries(obj)) {
			if (value !== null && value !== undefined && value !== "") {
				cleaned[key] = removeNullEmptyProperties(value);
			}
		}
		return cleaned;
	}

	return obj;
}

/**
 * Creates a request function with pre-configured base URL and headers
 *
 * @param baseUrl - Base URL for all requests
 * @param defaultHeaders - Default headers for all requests
 * @returns Request function
 */
export function createApiClient(
	baseUrl: string,
	defaultHeaders: Record<string, string> = {},
) {
	return async <T = unknown>(options: RequestOptions): Promise<ApiResponse<T>> => {
		return makeRequest<T>(baseUrl, options, defaultHeaders);
	};
}

/**
 * Utility function to handle API responses and extract data
 *
 * @param response - API response
 * @param dataPath - Path to extract data from response (e.g., "contact", "patient")
 * @returns Extracted data
 */
export function extractResponseData<T>(
	response: ApiResponse<unknown>,
	dataPath?: string,
): T {
	if (
		dataPath &&
		typeof response.data === "object" &&
		response.data &&
		dataPath in response.data
	) {
		return (response.data as Record<string, unknown>)[dataPath] as T;
	}
	return response.data as T;
}
