import { getDb } from "@database";
import { patient as patientSchema } from "@database/schema";
import type { GetCCPatientType } from "@type";
import { eq } from "drizzle-orm";
import { getConfig } from "./configs";

/**
 * In-memory buffer to track recent processing events
 * Replaces the Skip table from v3Integration with a simpler approach
 * Uses Map for O(1) lookup performance
 */
const processingBuffer = new Map<string, number>();

/**
 * Checks if an event is currently in the processing buffer
 * This prevents duplicate processing of events that were recently handled
 *
 * @param bufferKey - Unique key identifying the event (e.g., "EntityWasCreated:Patient:123")
 * @returns True if the event is in buffer and should be skipped, false otherwise
 */
export function isItInBuffer(bufferKey: string): Promise<boolean> {
	return Promise.resolve(isInBufferSync(bufferKey));
}

/**
 * Synchronous version of buffer check for internal use
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event is in buffer, false otherwise
 */
function isInBufferSync(bufferKey: string): boolean {
	const now = Date.now();
	const bufferTime = Number(getConfig("syncBufferTimeSec")) * 1000; // Convert to milliseconds

	// Clean expired entries first
	cleanExpiredEntries(now, bufferTime);

	// Check if key exists and is still valid
	const timestamp = processingBuffer.get(bufferKey);
	if (timestamp && now - timestamp < bufferTime) {
		return true;
	}

	return false;
}

/**
 * Adds an event to the processing buffer
 * This should be called when starting to process an event
 *
 * @param bufferKey - Unique key identifying the event
 */
export function addToBuffer(bufferKey: string): void {
	processingBuffer.set(bufferKey, Date.now());
}

/**
 * Removes an event from the processing buffer
 * This can be called when processing is complete, though it's not strictly necessary
 * as entries will expire automatically
 *
 * @param bufferKey - Unique key identifying the event
 */
export function removeFromBuffer(bufferKey: string): void {
	processingBuffer.delete(bufferKey);
}

/**
 * Cleans expired entries from the buffer
 * This is called automatically during buffer checks to prevent memory leaks
 *
 * @param now - Current timestamp
 * @param bufferTime - Buffer time in milliseconds
 */
function cleanExpiredEntries(now: number, bufferTime: number): void {
	for (const [key, timestamp] of processingBuffer.entries()) {
		if (now - timestamp >= bufferTime) {
			processingBuffer.delete(key);
		}
	}
}

/**
 * Gets the current size of the processing buffer
 * Useful for monitoring and debugging
 *
 * @returns Number of entries currently in the buffer
 */
export function getBufferSize(): number {
	return processingBuffer.size;
}

/**
 * Clears all entries from the processing buffer
 * Should only be used for testing or emergency cleanup
 */
export function clearBuffer(): void {
	processingBuffer.clear();
}

/**
 * Gets buffer statistics for monitoring
 *
 * @returns Object containing buffer statistics
 */
export function getBufferStats(): {
	size: number;
	oldestEntry: number | null;
	newestEntry: number | null;
} {
	let oldest: number | null = null;
	let newest: number | null = null;

	for (const timestamp of processingBuffer.values()) {
		if (oldest === null || timestamp < oldest) {
			oldest = timestamp;
		}
		if (newest === null || timestamp > newest) {
			newest = timestamp;
		}
	}

	return {
		size: processingBuffer.size,
		oldestEntry: oldest,
		newestEntry: newest,
	};
}

/**
 * Generates a buffer key for patient operations
 *
 * @param operation - Operation type (e.g., "ProcessPatientCreate", "ProcessPatientUpdate")
 * @param patientId - Patient ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generatePatientBufferKey(
	operation: string,
	patientId: string | number,
): string {
	return `${operation}:${patientId}`;
}

/**
 * Generates a buffer key for appointment operations
 *
 * @param operation - Operation type (e.g., "ProcessAppointmentCreate", "ProcessAppointmentUpdate")
 * @param appointmentId - Appointment ID (CC ID or AP ID)
 * @returns Buffer key string
 */
export function generateAppointmentBufferKey(
	operation: string,
	appointmentId: string | number,
): string {
	return `${operation}:${appointmentId}`;
}

/**
 * Generates a buffer key for invoice/payment operations
 *
 * @param operation - Operation type (e.g., "ProcessInvoicePayment")
 * @param entityId - Entity ID
 * @returns Buffer key string
 */
export function generateInvoicePaymentBufferKey(
	operation: string,
	entityId: string | number,
): string {
	return `${operation}:${entityId}`;
}

/**
 * Helper function to check and add to buffer in one operation
 * This is the most common pattern - check if in buffer, and if not, add it
 *
 * @param bufferKey - Unique key identifying the event
 * @returns True if the event was already in buffer (should skip), false if added to buffer (should process)
 */
export function checkAndAddToBuffer(bufferKey: string): boolean {
	if (isInBufferSync(bufferKey)) {
		return true; // Already in buffer, should skip
	}

	addToBuffer(bufferKey);
	return false; // Not in buffer, added now, should process
}

/**
 * Timestamp-based buffer check for patient data
 * This method compares the payload's updatedAt timestamp with the local patient's ccUpdatedAt
 * Uses a 1-minute tolerance buffer as specified in requirements
 *
 * @param payload - Patient payload from CC webhook
 * @param patient - Local patient record (optional, will be fetched if not provided)
 * @returns True if within 1-minute buffer (should skip), false if outside buffer (should process)
 */
export async function isInBuffer(
	payload: GetCCPatientType,
	patient?: { ccUpdatedAt: string | null },
): Promise<boolean> {
	const payloadUpdatedAt = payload.updatedAt;
	let localUpdatedAt = patient?.ccUpdatedAt;

	// If no patient provided, fetch from database
	if (!patient && payload.id) {
		try {
			const db = getDb();
			const dbPatient = await db
				.select({ ccUpdatedAt: patientSchema.ccUpdatedAt })
				.from(patientSchema)
				.where(eq(patientSchema.ccId, payload.id))
				.limit(1)
				.then((results) => results[0]);

			localUpdatedAt = dbPatient?.ccUpdatedAt?.toISOString() || null;
		} catch (error) {
			console.error("Error fetching patient for buffer check:", error);
			return false; // If we can't fetch, assume not in buffer
		}
	}

	// If no local timestamp exists, not in buffer
	if (!localUpdatedAt) {
		return false;
	}

	// Convert payload timestamp to Date object
	const payloadDate = new Date(payloadUpdatedAt);
	const localDate = new Date(localUpdatedAt);

	// Calculate the absolute difference in milliseconds
	const timeDifferenceMs = Math.abs(
		payloadDate.getTime() - localDate.getTime(),
	);

	// Convert 1 minute to milliseconds
	const oneMinuteMs = 60 * 1000;

	// Return true if within 1 minute buffer, false if outside buffer
	return timeDifferenceMs <= oneMinuteMs;
}
