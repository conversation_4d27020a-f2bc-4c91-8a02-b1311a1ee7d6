import { logger } from "./logger";

/**
 * Slack Logger for error notifications
 * Provides a simple interface for logging errors that should be sent to Slack
 * In the modern architecture, this integrates with the existing logger system
 */
export class SlackLogger {
	/**
	 * Log an error message that should be sent to Slack
	 * @param message - Error message
	 * @param data - Additional error data
	 */
	error(message: string, data?: unknown): void {
		// Use the existing logger with error level
		// The logger can be configured to send errors to external services like Slack
		logger.error(message, {
			...this.formatData(data),
			slack: true, // Flag to indicate this should go to Slack
		});
	}

	/**
	 * Log a warning message that should be sent to Slack
	 * @param message - Warning message
	 * @param data - Additional warning data
	 */
	warn(message: string, data?: unknown): void {
		logger.warn(message, {
			...this.formatData(data),
			slack: true,
		});
	}

	/**
	 * Log an info message that should be sent to Slack
	 * @param message - Info message
	 * @param data - Additional info data
	 */
	info(message: string, data?: unknown): void {
		logger.info(message, {
			...this.formatData(data),
			slack: true,
		});
	}

	/**
	 * Format data for logging
	 * @param data - Data to format
	 * @returns Formatted data object
	 */
	private formatData(data?: unknown): Record<string, unknown> {
		if (!data) return {};

		if (typeof data === "string") {
			return { message: data };
		}

		if (typeof data === "object" && data !== null) {
			return data as Record<string, unknown>;
		}

		return { data: String(data) };
	}
}

// Export singleton instance
export const slackLogger = new SlackLogger();

// Export default for backward compatibility
export default slackLogger;
