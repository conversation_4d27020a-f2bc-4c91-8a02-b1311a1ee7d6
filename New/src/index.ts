import { Hono } from "hono";
import { contextStorage } from "hono/context-storage";
import { getBufferStats } from "./utils/bufferManager";
import {
	handleAPAppointmentWebhook,
	handleAPContactWebhook,
} from "./webhook/apHandler";
import { handleWebhookEvent } from "./webhook/handler";

const app = new Hono<Env>();
app.use(contextStorage());

app.onError((err, c) => {
	console.error(err);
	return c.json(
		{
			message: "Internal Server Error",
			requestId: crypto.randomUUID(),
			timestamp: new Date().toISOString(),
		},
		500,
	);
});

// Health check endpoints
app.get("/", (c) => c.text("DermaCare Data Sync Service - OK"));
app.get("/health", (c) =>
	c.json({
		status: "healthy",
		timestamp: new Date().toISOString(),
		version: "2.0.0",
		bufferStats: getBufferStats(),
	}),
);

// Webhook endpoint for receiving data synchronization events from CC
// Replaces socket.io events with HTTP webhooks
app.post("/webhook", handleWebhookEvent);

// AP webhook endpoints for bi-directional sync (AP → CC)
// Based on v3Integration routes: /:location/ap/appointment/create and /:location/ap/appointment/update
app.post("/:location/ap/appointment/create", handleAPAppointmentWebhook);
app.post("/:location/ap/appointment/update", handleAPAppointmentWebhook);
app.post("/:location/ap/appointment/delete", handleAPAppointmentWebhook);

// AP contact webhook endpoints (new functionality not in v3Integration)
app.post("/:location/ap/contact/create", handleAPContactWebhook);
app.post("/:location/ap/contact/update", handleAPContactWebhook);
app.post("/:location/ap/contact/delete", handleAPContactWebhook);

// Sync services endpoint (from v3Integration - basic implementation)
app.post("/:location/sync-services", (c) => {
	return c.json({
		message: "Sync services endpoint - functionality to be implemented",
		status: "ok",
		timestamp: new Date().toISOString(),
	});
});

export default app;
