import type {
	GetCCAppointmentType,
	GetCCPatientType,
	GetPaymentType,
	WebhookContext,
	WebhookEvent,
} from "@type";
import { isItInBuffer } from "@utils/bufferManager";
import { logError } from "@utils/errorLogger";
import type { Context } from "hono";
import {
	processAppointmentCreate,
	processAppointmentDelete,
	processAppointmentUpdate,
} from "../processors/appointmentProcessor";
import { processInvoicePayment } from "../processors/invoicePaymentProcessor";
import {
	processPatientCreate,
	processPatientUpdate,
} from "../processors/patientProcessor";

/**
 * Webhook event handler
 * Processes incoming webhook events and routes them to appropriate processors
 *
 * @param c - Hono context
 * @returns Response indicating success or failure
 */
export async function handleWebhookEvent(c: Context): Promise<Response> {
	const requestId = crypto.randomUUID();

	try {
		// Parse webhook payload
		const webhookEvent: WebhookEvent = await c.req.json();

		// Validate webhook event structure
		if (!isValidWebhookEvent(webhookEvent)) {
			await logError(
				"WEBHOOK_VALIDATION_ERROR",
				new Error("Invalid webhook event structure"),
				{ webhookEvent, requestId },
				"WebhookHandler",
			);
			return c.json(
				{ error: "Invalid webhook event structure", requestId },
				400,
			);
		}

		// Create processing context
		const context: WebhookContext = {
			event: webhookEvent,
			processedAt: new Date(),
			requestId,
		};

		// Check buffer to prevent duplicate processing
		const bufferKey = generateBufferKey(webhookEvent);
		if (await isItInBuffer(bufferKey)) {
			console.log(
				`Event ${webhookEvent.event}:${webhookEvent.model}:${webhookEvent.id} is in buffer, skipping processing`,
			);
			return c.json(
				{
					message: "Event already processed recently",
					requestId,
					skipped: true,
				},
				200,
			);
		}

		// Route to appropriate processor based on event and model
		const result = await routeWebhookEvent(context);

		return c.json(
			{
				message: "Webhook processed successfully",
				requestId,
				result,
			},
			200,
		);
	} catch (error) {
		await logError(
			"WEBHOOK_PROCESSING_ERROR",
			error,
			{ requestId },
			"WebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * Validates webhook event structure
 *
 * @param event - Webhook event to validate
 * @returns True if valid, false otherwise
 */
function isValidWebhookEvent(event: unknown): event is WebhookEvent {
	return (
		event !== null &&
		typeof event === "object" &&
		"event" in event &&
		"model" in event &&
		"id" in event &&
		"payload" in event &&
		typeof (event as Record<string, unknown>).event === "string" &&
		typeof (event as Record<string, unknown>).model === "string" &&
		typeof (event as Record<string, unknown>).id === "number" &&
		typeof (event as Record<string, unknown>).payload === "object" &&
		(event as Record<string, unknown>).payload !== null
	);
}

/**
 * Generates a unique buffer key for the webhook event
 *
 * @param event - Webhook event
 * @returns Buffer key string
 */
function generateBufferKey(event: WebhookEvent): string {
	return `${event.event}:${event.model}:${event.id}`;
}

/**
 * Routes webhook events to appropriate processors
 *
 * @param context - Webhook processing context
 * @returns Processing result
 */
async function routeWebhookEvent(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	try {
		switch (event.event) {
			case "EntityWasCreated":
				return await handleEntityCreated(context);

			case "EntityWasUpdated":
				return await handleEntityUpdated(context);

			case "EntityWasDeleted":
				return await handleEntityDeleted(context);

			case "AppointmentWasCreated":
				return await handleAppointmentCreated(context);

			case "AppointmentWasUpdated":
				return await handleAppointmentUpdated(context);

			case "AppointmentWasDeleted":
				return await handleAppointmentDeleted(context);

			default:
				throw new Error(`Unsupported event type: ${event.event}`);
		}
	} catch (error) {
		await logError(
			"WEBHOOK_ROUTING_ERROR",
			error,
			{ event: event.event, model: event.model, id: event.id },
			"WebhookRouter",
		);
		throw error;
	}
}

/**
 * Handles EntityWasCreated events
 */
async function handleEntityCreated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Patient":
			return await processPatientCreate(
				event.payload as GetCCPatientType,
				context,
			);

		case "Invoice":
		case "Payment":
			return await processInvoicePayment(
				event.payload as GetPaymentType,
				context,
			);

		case "Service":
			console.log("Service events are not processed", event);
			return { message: "Service events are not processed", skipped: true };

		default:
			console.log("Unknown EntityWasCreated model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles EntityWasUpdated events
 */
async function handleEntityUpdated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Patient":
			return await processPatientUpdate(
				event.payload as GetCCPatientType,
				context,
			);

		case "Appointment":
			return await processAppointmentUpdate(
				event.payload as GetCCAppointmentType,
				context,
			);

		case "Service":
			console.log("Service events are not processed", event);
			return { message: "Service events are not processed", skipped: true };

		default:
			console.log("Unknown EntityWasUpdated model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles EntityWasDeleted events
 */
async function handleEntityDeleted(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	switch (event.model) {
		case "Appointment":
			return await processAppointmentDelete(event.id, context);

		default:
			console.log("Unknown EntityWasDeleted model:", event.model);
			return { message: `Unknown model: ${event.model}`, skipped: true };
	}
}

/**
 * Handles AppointmentWasCreated events
 */
async function handleAppointmentCreated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentCreate(
			event.payload as GetCCAppointmentType,
			context,
		);
	}

	return {
		message: `Unexpected model for AppointmentWasCreated: ${event.model}`,
		skipped: true,
	};
}

/**
 * Handles AppointmentWasUpdated events
 */
async function handleAppointmentUpdated(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentUpdate(
			event.payload as GetCCAppointmentType,
			context,
		);
	}

	return {
		message: `Unexpected model for AppointmentWasUpdated: ${event.model}`,
		skipped: true,
	};
}

/**
 * Handles AppointmentWasDeleted events
 */
async function handleAppointmentDeleted(context: WebhookContext): Promise<{
	success?: boolean;
	message: string;
	skipped?: boolean;
	[key: string]: unknown;
}> {
	const { event } = context;

	if (event.model === "Appointment") {
		return await processAppointmentDelete(event.id, context);
	}

	return {
		message: `Unexpected model for AppointmentWasDeleted: ${event.model}`,
		skipped: true,
	};
}
