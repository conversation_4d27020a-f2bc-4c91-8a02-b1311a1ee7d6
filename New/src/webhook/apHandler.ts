import type {
	GetAPAppointmentType,
	GetAPContactType,
	WebhookContext,
} from "@type";
import { logError } from "@utils/errorLogger";
import type { Context } from "hono";
import {
	processAPAppointmentCreate,
	processAPAppointmentDelete,
	processAPAppointmentUpdate,
} from "../processors/apAppointmentProcessor";
import {
	processAPContactCreate,
	processAPContactDelete,
	processAPContactUpdate,
} from "../processors/apContactProcessor";

/**
 * AP Webhook Event structure based on v3Integration patterns
 */
interface APWebhookEvent {
	/** Event type from AP */
	type:
		| "appointment_created"
		| "appointment_updated"
		| "appointment_deleted"
		| "contact_created"
		| "contact_updated"
		| "contact_deleted";
	/** Event data */
	data: {
		/** Calendar data for appointments */
		calendar?: GetAPAppointmentType & {
			appointmentId: string;
			startTime: string;
			endTime: string;
			created_by_meta?: { source?: string };
			last_updated_by_meta?: { source?: string };
		};
		/** Contact data */
		contact?: GetAPContactType;
		/** Contact ID for appointments */
		contact_id?: string;
		/** Additional fields */
		email?: string;
		phone?: string;
	};
}

/**
 * Handles AP webhook events for appointment operations
 * Based on v3Integration ProcessApAppointmentCreatesController and ProcessApAppointmentUpdatesController
 *
 * @param c - Hono context
 * @returns Response
 */
export async function handleAPAppointmentWebhook(
	c: Context,
): Promise<Response> {
	const requestId = crypto.randomUUID();

	try {
		const body = await c.req.json();
		console.log(
			"AP Appointment Webhook received:",
			JSON.stringify(body, null, 2),
		);

		// Validate webhook structure
		if (!isValidAPWebhookEvent(body)) {
			return c.json(
				{
					error: "Invalid webhook event structure",
					requestId,
				},
				400,
			);
		}

		const event = body as APWebhookEvent;
		const { calendar, contact_id: contactId } = event.data;

		if (!calendar) {
			return c.json(
				{
					error: "Calendar data not found",
					requestId,
				},
				400,
			);
		}

		// Skip if created/updated by third party (prevents loops)
		if (
			calendar.created_by_meta?.source === "third_party" ||
			calendar.last_updated_by_meta?.source === "third_party"
		) {
			return c.json(
				{
					message: "Appointment created/updated by third party, skipping",
					requestId,
					skipped: true,
				},
				200,
			);
		}

		if (!contactId) {
			return c.json(
				{
					error: "Contact ID not found",
					requestId,
				},
				400,
			);
		}

		// Create webhook context
		const context: WebhookContext = {
			event: {
				event:
					event.type === "appointment_created"
						? "EntityWasCreated"
						: event.type === "appointment_updated"
							? "EntityWasUpdated"
							: "EntityWasDeleted",
				model: "Appointment",
				id: parseInt(calendar.appointmentId),
				payload: calendar,
			},
			processedAt: new Date(),
			requestId,
		};

		// Convert calendar data to standard appointment format
		const appointmentPayload: GetAPAppointmentType = {
			id: calendar.appointmentId,
			contactId: contactId,
			startTime: calendar.startTime,
			endTime: calendar.endTime,
			title: calendar.title || "Appointment from AP",
			notes: calendar.notes || "",
			status: calendar.status || "scheduled",
			createdAt: calendar.createdAt || new Date().toISOString(),
			updatedAt: calendar.updatedAt || new Date().toISOString(),
			...calendar,
		};

		let result: { success?: boolean; message: string; skipped?: boolean };

		// Route to appropriate processor
		switch (event.type) {
			case "appointment_created":
				result = await processAPAppointmentCreate(appointmentPayload, context);
				break;

			case "appointment_updated":
				result = await processAPAppointmentUpdate(appointmentPayload, context);
				break;

			case "appointment_deleted":
				result = await processAPAppointmentDelete(appointmentPayload, context);
				break;

			default:
				return c.json(
					{
						error: `Unknown appointment event type: ${event.type}`,
						requestId,
					},
					400,
				);
		}

		return c.json(
			{
				message: "AP appointment webhook processed successfully",
				requestId,
				result,
			},
			200,
		);
	} catch (error) {
		await logError(
			"AP_APPOINTMENT_WEBHOOK_ERROR",
			error,
			{ requestId },
			"APWebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * Handles AP webhook events for contact operations
 * This creates the missing AP → CC contact sync functionality
 *
 * @param c - Hono context
 * @returns Response
 */
export async function handleAPContactWebhook(c: Context): Promise<Response> {
	const requestId = crypto.randomUUID();

	try {
		const body = await c.req.json();
		console.log("AP Contact Webhook received:", JSON.stringify(body, null, 2));

		// Validate webhook structure
		if (!isValidAPWebhookEvent(body)) {
			return c.json(
				{
					error: "Invalid webhook event structure",
					requestId,
				},
				400,
			);
		}

		const event = body as APWebhookEvent;
		const { contact } = event.data;

		if (!contact) {
			return c.json(
				{
					error: "Contact data not found",
					requestId,
				},
				400,
			);
		}

		// Create webhook context
		const context: WebhookContext = {
			event: {
				event:
					event.type === "contact_created"
						? "EntityWasCreated"
						: event.type === "contact_updated"
							? "EntityWasUpdated"
							: "EntityWasDeleted",
				model: "Patient",
				id: parseInt(contact.id),
				payload: contact,
			},
			processedAt: new Date(),
			requestId,
		};

		let result: { success?: boolean; message: string; skipped?: boolean };

		// Route to appropriate processor
		switch (event.type) {
			case "contact_created":
				result = await processAPContactCreate(contact, context);
				break;

			case "contact_updated":
				result = await processAPContactUpdate(contact, context);
				break;

			case "contact_deleted":
				result = await processAPContactDelete(contact, context);
				break;

			default:
				return c.json(
					{
						error: `Unknown contact event type: ${event.type}`,
						requestId,
					},
					400,
				);
		}

		return c.json(
			{
				message: "AP contact webhook processed successfully",
				requestId,
				result,
			},
			200,
		);
	} catch (error) {
		await logError(
			"AP_CONTACT_WEBHOOK_ERROR",
			error,
			{ requestId },
			"APWebhookHandler",
		);

		return c.json(
			{
				error: "Internal server error",
				requestId,
			},
			500,
		);
	}
}

/**
 * Type guard to validate AP webhook event structure
 *
 * @param event - Unknown event data
 * @returns True if valid AP webhook event
 */
function isValidAPWebhookEvent(event: unknown): event is APWebhookEvent {
	return (
		event !== null &&
		typeof event === "object" &&
		"type" in event &&
		"data" in event &&
		typeof (event as Record<string, unknown>).type === "string" &&
		typeof (event as Record<string, unknown>).data === "object" &&
		(event as Record<string, unknown>).data !== null
	);
}
